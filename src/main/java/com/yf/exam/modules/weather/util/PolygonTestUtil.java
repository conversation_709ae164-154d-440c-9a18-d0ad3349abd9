package com.yf.exam.modules.weather.util;

import java.util.*;

/**
 * 多边形测试工具类
 * 用于验证多边形优化功能
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-29
 */
public class PolygonTestUtil {

    /**
     * 测试多边形优化功能
     */
    public static void main(String[] args) {
        System.out.println("=== 多边形优化功能测试 ===");

        // 测试1：简单多边形（正方形）
        testSimplePolygon();

        // 测试2：自相交多边形（8字形）
        testSelfIntersectingPolygon();

        // 测试3：面积计算
        testAreaCalculation();

        // 测试4：多区域过滤测试
        testMultiplePolygonFiltering();

        System.out.println("=== 测试完成 ===");
    }

    /**
     * 测试简单多边形
     */
    private static void testSimplePolygon() {
        System.out.println("\n--- 测试简单多边形 ---");
        
        List<List<Double>> square = new ArrayList<>();
        square.add(Arrays.asList(0.0, 0.0));
        square.add(Arrays.asList(1.0, 0.0));
        square.add(Arrays.asList(1.0, 1.0));
        square.add(Arrays.asList(0.0, 1.0));
        square.add(Arrays.asList(0.0, 0.0)); // 闭合
        
        System.out.println("正方形坐标: " + square);
        System.out.println("点数: " + square.size());
        
        double area = calculatePolygonArea(square);
        System.out.println("计算面积: " + area + " (期望: 1.0)");
        
        boolean hasSelfIntersection = hasSelfIntersection(square);
        System.out.println("是否自相交: " + hasSelfIntersection + " (期望: false)");
    }

    /**
     * 测试自相交多边形
     */
    private static void testSelfIntersectingPolygon() {
        System.out.println("\n--- 测试自相交多边形 ---");
        
        // 创建一个8字形多边形
        List<List<Double>> figure8 = new ArrayList<>();
        figure8.add(Arrays.asList(0.0, 0.0));
        figure8.add(Arrays.asList(2.0, 2.0));
        figure8.add(Arrays.asList(4.0, 0.0));
        figure8.add(Arrays.asList(4.0, 4.0));
        figure8.add(Arrays.asList(2.0, 2.0));
        figure8.add(Arrays.asList(0.0, 4.0));
        figure8.add(Arrays.asList(0.0, 0.0)); // 闭合
        
        System.out.println("8字形坐标: " + figure8);
        System.out.println("点数: " + figure8.size());
        
        boolean hasSelfIntersection = hasSelfIntersection(figure8);
        System.out.println("是否自相交: " + hasSelfIntersection + " (期望: true)");
        
        if (hasSelfIntersection) {
            System.out.println("检测到自相交，这将触发多边形优化处理");
        }
    }

    /**
     * 测试面积计算
     */
    private static void testAreaCalculation() {
        System.out.println("\n--- 测试面积计算 ---");
        
        // 测试三角形
        List<List<Double>> triangle = new ArrayList<>();
        triangle.add(Arrays.asList(0.0, 0.0));
        triangle.add(Arrays.asList(2.0, 0.0));
        triangle.add(Arrays.asList(1.0, 2.0));
        triangle.add(Arrays.asList(0.0, 0.0)); // 闭合
        
        double triangleArea = calculatePolygonArea(triangle);
        System.out.println("三角形面积: " + triangleArea + " (期望: 2.0)");
        
        // 测试矩形
        List<List<Double>> rectangle = new ArrayList<>();
        rectangle.add(Arrays.asList(0.0, 0.0));
        rectangle.add(Arrays.asList(3.0, 0.0));
        rectangle.add(Arrays.asList(3.0, 2.0));
        rectangle.add(Arrays.asList(0.0, 2.0));
        rectangle.add(Arrays.asList(0.0, 0.0)); // 闭合
        
        double rectangleArea = calculatePolygonArea(rectangle);
        System.out.println("矩形面积: " + rectangleArea + " (期望: 6.0)");
    }

    /**
     * 测试多区域过滤功能
     */
    private static void testMultiplePolygonFiltering() {
        System.out.println("\n--- 测试多区域过滤功能 ---");

        // 模拟3个不同大小的多边形区域
        List<List<List<Double>>> polygons = new ArrayList<>();

        // 主要区域1：大正方形 (面积 = 4)
        List<List<Double>> mainPolygon1 = new ArrayList<>();
        mainPolygon1.add(Arrays.asList(0.0, 0.0));
        mainPolygon1.add(Arrays.asList(2.0, 0.0));
        mainPolygon1.add(Arrays.asList(2.0, 2.0));
        mainPolygon1.add(Arrays.asList(0.0, 2.0));
        mainPolygon1.add(Arrays.asList(0.0, 0.0));
        polygons.add(mainPolygon1);

        // 主要区域2：中等矩形 (面积 = 3)
        List<List<Double>> mainPolygon2 = new ArrayList<>();
        mainPolygon2.add(Arrays.asList(3.0, 0.0));
        mainPolygon2.add(Arrays.asList(6.0, 0.0));
        mainPolygon2.add(Arrays.asList(6.0, 1.0));
        mainPolygon2.add(Arrays.asList(3.0, 1.0));
        mainPolygon2.add(Arrays.asList(3.0, 0.0));
        polygons.add(mainPolygon2);

        // 小尾巴：很小的三角形 (面积 = 0.05)
        List<List<Double>> smallTail = new ArrayList<>();
        smallTail.add(Arrays.asList(7.0, 0.0));
        smallTail.add(Arrays.asList(7.1, 0.0));
        smallTail.add(Arrays.asList(7.05, 0.1));
        smallTail.add(Arrays.asList(7.0, 0.0));
        polygons.add(smallTail);

        System.out.println("原始多边形数量: " + polygons.size());
        for (int i = 0; i < polygons.size(); i++) {
            double area = calculatePolygonArea(polygons.get(i));
            System.out.println("多边形 " + (i + 1) + " 面积: " + area);
        }

        // 模拟过滤逻辑
        List<List<List<Double>>> filtered = filterOutSmallTails(polygons);
        System.out.println("\n过滤后多边形数量: " + filtered.size());
        for (int i = 0; i < filtered.size(); i++) {
            double area = calculatePolygonArea(filtered.get(i));
            System.out.println("保留的多边形 " + (i + 1) + " 面积: " + area);
        }

        System.out.println("期望结果: 保留2个主要区域，删除1个小尾巴");
    }

    /**
     * 过滤掉小尾巴，保留主要区域
     */
    private static List<List<List<Double>>> filterOutSmallTails(List<List<List<Double>>> polygons) {
        if (polygons.isEmpty()) {
            return new ArrayList<>();
        }

        if (polygons.size() == 1) {
            return polygons;
        }

        List<List<List<Double>>> result = new ArrayList<>();
        List<PolygonWithArea> polygonsWithArea = new ArrayList<>();

        // 计算所有多边形的面积
        for (List<List<Double>> polygon : polygons) {
            double area = calculatePolygonArea(polygon);
            polygonsWithArea.add(new PolygonWithArea(polygon, area));
        }

        // 按面积排序（降序）
        polygonsWithArea.sort((a, b) -> Double.compare(b.area, a.area));

        // 计算面积阈值：最大面积的10%作为最小保留阈值
        double maxArea = polygonsWithArea.get(0).area;
        double minAreaThreshold = maxArea * 0.1;

        System.out.println("最大面积: " + maxArea + ", 阈值: " + minAreaThreshold);

        boolean hasMainPolygon = false;

        for (PolygonWithArea polygonWithArea : polygonsWithArea) {
            if (!hasMainPolygon || polygonWithArea.area >= minAreaThreshold) {
                result.add(polygonWithArea.polygon);
                hasMainPolygon = true;
                System.out.println("保留多边形，面积: " + polygonWithArea.area);
            } else {
                System.out.println("过滤小尾巴，面积: " + polygonWithArea.area);
            }
        }

        return result;
    }

    /**
     * 带面积信息的多边形类
     */
    private static class PolygonWithArea {
        List<List<Double>> polygon;
        double area;

        public PolygonWithArea(List<List<Double>> polygon, double area) {
            this.polygon = polygon;
            this.area = area;
        }
    }

    /**
     * 计算多边形面积（使用鞋带公式）
     */
    private static double calculatePolygonArea(List<List<Double>> coordinates) {
        if (coordinates.size() < 3) {
            return 0;
        }
        
        double area = 0;
        int n = coordinates.size();
        
        for (int i = 0; i < n - 1; i++) {
            List<Double> p1 = coordinates.get(i);
            List<Double> p2 = coordinates.get((i + 1) % n);
            area += p1.get(0) * p2.get(1) - p2.get(0) * p1.get(1);
        }
        
        return Math.abs(area) / 2.0;
    }

    /**
     * 检查多边形是否存在自相交
     */
    private static boolean hasSelfIntersection(List<List<Double>> coordinates) {
        if (coordinates.size() < 4) {
            return false; // 少于4个点无法形成自相交
        }
        
        // 检查每条边是否与其他边相交（除了相邻边）
        for (int i = 0; i < coordinates.size() - 1; i++) {
            List<Double> p1 = coordinates.get(i);
            List<Double> p2 = coordinates.get((i + 1) % (coordinates.size() - 1));
            
            for (int j = i + 2; j < coordinates.size() - 1; j++) {
                // 跳过相邻边和最后一条边与第一条边的检查（它们本来就应该相交）
                if (j == coordinates.size() - 2 && i == 0) {
                    continue;
                }
                
                List<Double> p3 = coordinates.get(j);
                List<Double> p4 = coordinates.get((j + 1) % (coordinates.size() - 1));
                
                if (doLinesIntersect(p1, p2, p3, p4)) {
                    return true;
                }
            }
        }
        
        return false;
    }

    /**
     * 检查两条线段是否相交
     */
    private static boolean doLinesIntersect(List<Double> p1, List<Double> p2, List<Double> p3, List<Double> p4) {
        double x1 = p1.get(0), y1 = p1.get(1);
        double x2 = p2.get(0), y2 = p2.get(1);
        double x3 = p3.get(0), y3 = p3.get(1);
        double x4 = p4.get(0), y4 = p4.get(1);
        
        // 计算方向
        double d1 = direction(x3, y3, x4, y4, x1, y1);
        double d2 = direction(x3, y3, x4, y4, x2, y2);
        double d3 = direction(x1, y1, x2, y2, x3, y3);
        double d4 = direction(x1, y1, x2, y2, x4, y4);
        
        // 检查是否相交
        if (((d1 > 0 && d2 < 0) || (d1 < 0 && d2 > 0)) &&
            ((d3 > 0 && d4 < 0) || (d3 < 0 && d4 > 0))) {
            return true;
        }
        
        // 检查共线情况
        if (d1 == 0 && onSegment(x3, y3, x1, y1, x4, y4)) return true;
        if (d2 == 0 && onSegment(x3, y3, x2, y2, x4, y4)) return true;
        if (d3 == 0 && onSegment(x1, y1, x3, y3, x2, y2)) return true;
        if (d4 == 0 && onSegment(x1, y1, x4, y4, x2, y2)) return true;
        
        return false;
    }

    /**
     * 计算方向
     */
    private static double direction(double xi, double yi, double xj, double yj, double xk, double yk) {
        return (xk - xi) * (yj - yi) - (xj - xi) * (yk - yi);
    }

    /**
     * 检查点是否在线段上
     */
    private static boolean onSegment(double xi, double yi, double xk, double yk, double xj, double yj) {
        return Math.min(xi, xj) <= xk && xk <= Math.max(xi, xj) &&
               Math.min(yi, yj) <= yk && yk <= Math.max(yi, yj);
    }
}
