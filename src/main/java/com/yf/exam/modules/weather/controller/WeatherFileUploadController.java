package com.yf.exam.modules.weather.controller;

import com.yf.exam.core.api.ApiRest;
import com.yf.exam.core.api.controller.BaseController;
import com.yf.exam.modules.user.UserUtils;
import com.yf.exam.modules.weather.config.WeatherUploadConfig;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.UUID;

/**
 * 历史个例文件上传控制器
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-29
 */
@Slf4j
@RestController
@RequestMapping("/exam/api/weather/case")
@Api(tags = "历史个例文件上传")
public class WeatherFileUploadController extends BaseController {

    @Autowired
    private WeatherUploadConfig weatherUploadConfig;

    /**
     * 动态生成访问URL
     */
    private String generateAccessUrl(HttpServletRequest request, String relativePath) {
        String scheme = request.getScheme();
        String serverName = request.getServerName();
        int serverPort = request.getServerPort();
        String contextPath = request.getContextPath();

        StringBuilder url = new StringBuilder();
        url.append(scheme).append("://").append(serverName);

        // 只有在非标准端口时才添加端口号
        if ((scheme.equals("http") && serverPort != 80) ||
            (scheme.equals("https") && serverPort != 443)) {
            url.append(":").append(serverPort);
        }

        url.append(contextPath);
        if (!contextPath.endsWith("/")) {
            url.append("/");
        }
        url.append("upload/file/weather/").append(relativePath);

        return url.toString();
    }

    /**
     * 上传MICAPS文件
     */
    @RequiresRoles("sa")
    @ApiOperation(value = "上传MICAPS文件")
    @PostMapping("/upload/micaps")
    public ApiRest<Map<String, Object>> uploadMicapsFile(@RequestParam("file") MultipartFile file,
                                                        HttpServletRequest request) {
        try {
            // 验证文件
            if (file.isEmpty()) {
                return super.failure("上传文件不能为空");
            }

            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null || originalFilename.trim().isEmpty()) {
                return super.failure("文件名不能为空");
            }

            // 验证文件大小（限制为300MB）
            long maxSize = 300 * 1024 * 1024; // 300MB
            if (file.getSize() > maxSize) {
                return super.failure("文件大小不能超过300MB");
            }

            // 创建上传目录
            String uploadDir = weatherUploadConfig.getMicapsDir();
            File dir = new File(uploadDir);
            if (!dir.exists()) {
                boolean created = dir.mkdirs();
                log.info("创建MICAPS上传目录: {} - {}", uploadDir, created ? "成功" : "失败");
            }

            // 生成唯一文件名
            String fileName = generateUniqueFileName(originalFilename);
            String filePath = uploadDir + fileName;

            // 保存文件
            Path targetPath = Paths.get(filePath);
            Files.copy(file.getInputStream(), targetPath);

            // 验证文件是否保存成功
            File savedFile = new File(filePath);
            if (!savedFile.exists()) {
                log.error("文件保存失败，路径: {}", filePath);
                return super.failure("文件保存失败");
            }

            log.info("MICAPS文件保存成功: {} -> {}", originalFilename, filePath);

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("id", System.currentTimeMillis());
            result.put("name", originalFilename);
            result.put("fileName", fileName);
            result.put("size", file.getSize());
            // 动态生成访问URL
            result.put("url", generateAccessUrl(request, "micaps/" + fileName));
            // 返回绝对路径，便于后续读取
            result.put("filePath", filePath);
            // 同时返回相对路径，保持兼容性
            result.put("relativePath", "weather/micaps/" + fileName);

            log.info("用户 {} 上传了MICAPS文件：{} -> {}",
                UserUtils.getUser().getUserName(), originalFilename, filePath);

            return super.success(result);

        } catch (IOException e) {
            log.error("上传MICAPS文件失败", e);
            return super.failure("文件上传失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("上传MICAPS文件异常", e);
            return super.failure("文件上传异常：" + e.getMessage());
        }
    }

    /**
     * 上传历史个例实况文件
     */
    @RequiresRoles("sa")
    @ApiOperation(value = "上传历史个例实况文件")
    @PostMapping("/upload/observation")
    public ApiRest<Map<String, Object>> uploadObservationFile(@RequestParam("file") MultipartFile file,
                                                              HttpServletRequest request) {
        try {
            // 验证文件
            if (file.isEmpty()) {
                return super.failure("上传文件不能为空");
            }

            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null || originalFilename.trim().isEmpty()) {
                return super.failure("文件名不能为空");
            }

            // 验证文件大小（限制为50MB）
            long maxSize = 50 * 1024 * 1024; // 50MB
            if (file.getSize() > maxSize) {
                return super.failure("文件大小不能超过50MB");
            }

            // 创建上传目录
            String uploadDir = weatherUploadConfig.getObservationDir();
            File dir = new File(uploadDir);
            if (!dir.exists()) {
                boolean created = dir.mkdirs();
                log.info("创建实况文件上传目录: {} - {}", uploadDir, created ? "成功" : "失败");
            }

            // 生成唯一文件名
            String fileName = generateUniqueFileName(originalFilename);
            String filePath = uploadDir + fileName;

            // 保存文件
            Path targetPath = Paths.get(filePath);
            Files.copy(file.getInputStream(), targetPath);

            // 验证文件是否保存成功
            File savedFile = new File(filePath);
            if (!savedFile.exists()) {
                log.error("实况文件保存失败，路径: {}", filePath);
                return super.failure("文件保存失败");
            }

            log.info("实况文件保存成功: {} -> {}", originalFilename, filePath);

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("id", System.currentTimeMillis());
            result.put("name", originalFilename);
            result.put("fileName", fileName);
            result.put("size", file.getSize());
            // 动态生成访问URL
            result.put("url", generateAccessUrl(request, "observation/" + fileName));
            // 返回绝对路径，便于后续读取
            result.put("filePath", filePath);
            // 同时返回相对路径，保持兼容性
            result.put("relativePath", "weather/observation/" + fileName);

            log.info("用户 {} 上传了实况文件：{} -> {}",
                UserUtils.getUser().getUserName(), originalFilename, filePath);

            return super.success(result);

        } catch (IOException e) {
            log.error("上传实况文件失败", e);
            return super.failure("文件上传失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("上传实况文件异常", e);
            return super.failure("文件上传异常：" + e.getMessage());
        }
    }

    /**
     * 上传历史个例数据文件（压缩包）
     */
    @RequiresRoles("sa")
    @ApiOperation(value = "上传历史个例数据文件")
    @PostMapping("/upload/data")
    public ApiRest<Map<String, Object>> uploadDataFile(@RequestParam("file") MultipartFile file,
                                                       HttpServletRequest request) {
        try {
            // 验证文件
            if (file.isEmpty()) {
                return super.failure("上传文件不能为空");
            }

            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null || originalFilename.trim().isEmpty()) {
                return super.failure("文件名不能为空");
            }

            // 验证文件大小（限制为500MB）
            long maxSize = 500 * 1024 * 1024; // 500MB
            if (file.getSize() > maxSize) {
                return super.failure("文件大小不能超过500MB");
            }

            // 验证文件类型（压缩文件）
            String extension = getFileExtension(originalFilename);
            if (!isArchiveFile(extension)) {
                return super.failure("不支持的文件格式，支持格式：.zip, .rar, .7z, .tar, .gz, .bz2");
            }

            // 创建上传目录
            String uploadDir = weatherUploadConfig.getDataDir();
            File dir = new File(uploadDir);
            if (!dir.exists()) {
                boolean created = dir.mkdirs();
                log.info("创建数据文件上传目录: {} - {}", uploadDir, created ? "成功" : "失败");
            }

            // 生成唯一文件名
            String fileName = generateUniqueFileName(originalFilename);
            String filePath = uploadDir + fileName;

            // 保存文件
            Path targetPath = Paths.get(filePath);
            Files.copy(file.getInputStream(), targetPath);

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("id", System.currentTimeMillis());
            result.put("name", originalFilename);
            result.put("fileName", fileName);
            result.put("size", file.getSize());
            // 动态生成访问URL
            result.put("url", generateAccessUrl(request, "data/" + fileName));
            // 返回绝对路径，便于后续读取
            result.put("filePath", filePath);
            // 同时返回相对路径，保持兼容性
            result.put("relativePath", "weather/data/" + fileName);

            log.info("用户 {} 上传了数据文件：{}", 
                UserUtils.getUser().getUserName(), originalFilename);

            return super.success(result);

        } catch (IOException e) {
            log.error("上传数据文件失败", e);
            return super.failure("文件上传失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("上传数据文件异常", e);
            return super.failure("文件上传异常：" + e.getMessage());
        }
    }

    /**
     * 上传考生降水落区文件
     */
    @ApiOperation(value = "上传考生降水落区文件")
    @PostMapping("/upload/precipitation-area")
    public ApiRest<Map<String, Object>> uploadPrecipitationAreaFile(@RequestParam("file") MultipartFile file,
                                                                    HttpServletRequest request) {
        try {
            // 验证文件
            if (file.isEmpty()) {
                return super.failure("上传文件不能为空");
            }

            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null || originalFilename.trim().isEmpty()) {
                return super.failure("文件名不能为空");
            }


            // 创建上传目录
            String uploadDir = weatherUploadConfig.getPrecipitationAreaDir();
            File dir = new File(uploadDir);
            if (!dir.exists()) {
                boolean created = dir.mkdirs();
                log.info("创建降水落区文件上传目录: {} - {}", uploadDir, created ? "成功" : "失败");
            }

            // 生成唯一文件名
            String fileName = generateUniqueFileName(originalFilename);
            String filePath = uploadDir + fileName;

            // 保存文件
            Path targetPath = Paths.get(filePath);
            Files.copy(file.getInputStream(), targetPath);

            // 读取文件内容并解析
            String content = new String(Files.readAllBytes(targetPath), StandardCharsets.UTF_8);
            Map<String, Object> parsedData = parsePrecipitationAreaFile(content);

            // 验证文件是否保存成功
            File savedFile = new File(filePath);
            if (!savedFile.exists()) {
                log.error("降水落区文件保存失败，路径: {}", filePath);
                return super.failure("文件保存失败");
            }

            log.info("降水落区文件保存成功: {} -> {}", originalFilename, filePath);

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("id", System.currentTimeMillis());
            result.put("name", originalFilename);
            result.put("fileName", fileName);
            result.put("size", file.getSize());
            // 动态生成访问URL
            result.put("url", generateAccessUrl(request, "precipitation-area/" + fileName));
            // 返回绝对路径，便于后续读取
            result.put("filePath", filePath);
            // 同时返回相对路径，保持兼容性
            result.put("relativePath", "weather/precipitation-area/" + fileName);
            result.put("parsedData", parsedData);

            log.info("用户 {} 上传了降水落区实况文件：{} -> {}",
                UserUtils.getUser().getUserName(), originalFilename, filePath);

            return super.success(result);

        } catch (IOException e) {
            log.error("上传降水落区实况文件失败", e);
            return super.failure("文件上传失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("上传降水落区实况文件异常", e);
            return super.failure("文件上传异常：" + e.getMessage());
        }
    }

    /**
     * 解析降水落区文件内容
     */
    private Map<String, Object> parsePrecipitationAreaFile(String content) {
        Map<String, Object> result = new HashMap<>();

        try {
            String[] lines = content.split("\n");
            Map<String, List<Map<String, Object>>> precipitationData = new HashMap<>();

            boolean inContoursSection = false;
            String currentLevel = null;
            List<List<Double>> currentCoordinates = new ArrayList<>();

            for (int i = 0; i < lines.length; i++) {
                String line = lines[i].trim();

                // 检测闭合轮廓段开始
                if (line.startsWith("CLOSED_CONTOURS:")) {
                    inContoursSection = true;
                    continue;
                }


                // 解析数据行 (如: "3 55" 表示3无用，55个坐标点; "100 1" 表示降水量100，1个无用坐标点)
                Pattern levelPattern = Pattern.compile("^([\\d.]+)\\s+(\\d+)$");
                Matcher levelMatcher = levelPattern.matcher(line);
                if (levelMatcher.matches()) {
                    double firstNum = Double.parseDouble(levelMatcher.group(1));
                    int pointCount = Integer.parseInt(levelMatcher.group(2));

                    // 如果点数为1，这是降水量结束标识
                    if (pointCount == 1) {
                        // 保存当前轮廓，使用第一个数字作为降水量
                        if (currentLevel != null && !currentCoordinates.isEmpty()) {
                            // 使用结束标识中的降水量值更新当前轮廓的量级
                            String precipitationLevel = mapLevelToString(firstNum);
                            // 创建坐标副本，避免引用被清空
                            List<List<Double>> coordinatesCopy = new ArrayList<>(currentCoordinates);
                            addContourToData(precipitationData, precipitationLevel, coordinatesCopy);
                            currentCoordinates.clear();
                            currentLevel = null;
                        }
                        // 跳过下一行的无用坐标数据
                        if (i + 1 < lines.length) {
                            i++; // 跳过下一行
                        }
                        continue;
                    }

                    // 保存上一个轮廓（如果有的话）
                    if (currentLevel != null && !currentCoordinates.isEmpty()) {
                        // 创建坐标副本，避免引用被清空
                        List<List<Double>> coordinatesCopy = new ArrayList<>(currentCoordinates);
                        addContourToData(precipitationData, currentLevel, coordinatesCopy);
                    }

                    // 开始新轮廓，第一个数字无用，只记录要读取的坐标点数
                    currentLevel = "parsing"; // 临时标识，真正的量级在结束标识中
                    currentCoordinates = new ArrayList<>();
                    continue;
                }

                // 解析坐标行（包含多个坐标点）
                Pattern coordPattern = Pattern.compile("^\\s*[\\d.-]+\\s+[\\d.-]+\\s+[\\d.-]+");
                Matcher coordMatcher = coordPattern.matcher(line);
                if (coordMatcher.find() && currentLevel != null && !"null".equals(currentLevel)) {
                    String[] coords = line.trim().split("\\s+");

                    for (int j = 0; j < coords.length; j += 3) {
                        if (j + 1 < coords.length) {
                            try {
                                double lng = Double.parseDouble(coords[j]);
                                double lat = Double.parseDouble(coords[j + 1]);
                                List<Double> point = Arrays.asList(lng, lat);
                                currentCoordinates.add(point);
                            } catch (NumberFormatException e) {
                                log.warn("解析坐标失败: {}", Arrays.toString(coords));
                            }
                        }
                    }
                }
            }

            // 保存最后一个轮廓（如果文件结束时还有未保存的轮廓）
            if (currentLevel != null && !currentCoordinates.isEmpty()) {
                // 如果没有结束标识，使用默认量级
                String defaultLevel = "level0";
                log.warn("发现没有结束标识的轮廓数据，使用默认量级: {}", defaultLevel);
                // 创建坐标副本，避免引用被清空
                List<List<Double>> coordinatesCopy = new ArrayList<>(currentCoordinates);
                addContourToData(precipitationData, defaultLevel, coordinatesCopy);
            }

            result.put("success", true);
            result.put("data", precipitationData);
            result.put("message", "文件解析成功");

            log.info("降水落区文件解析完成，共解析出 {} 个量级的数据", precipitationData.size());

        } catch (Exception e) {
            log.error("解析降水落区文件失败", e);
            result.put("success", false);
            result.put("message", "文件解析失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 添加轮廓到数据中
     */
    private void addContourToData(Map<String, List<Map<String, Object>>> precipitationData,
                                  String level, List<List<Double>> coordinates) {
        log.debug("addContourToData 被调用: level={}, coordinates.size()={}", level, coordinates.size());
        if (coordinates.isEmpty()) {
            log.warn("坐标列表为空，跳过添加轮廓");
            return;
        }

        // 确保轮廓闭合
        if (!coordinates.get(0).equals(coordinates.get(coordinates.size() - 1))) {
            coordinates.add(new ArrayList<>(coordinates.get(0)));
        }

        // 优化多边形，处理自相交问题
        List<List<List<Double>>> optimizedPolygons = optimizePolygon(coordinates);

        // 为每个优化后的多边形创建轮廓
        for (List<List<Double>> polygon : optimizedPolygons) {
            Map<String, Object> contour = new HashMap<>();
            contour.put("id", System.currentTimeMillis() + "_" + Math.random());

            Map<String, Object> geometry = new HashMap<>();
            geometry.put("type", "Polygon");
            // GeoJSON Polygon 格式: [[[lng, lat], [lng, lat], ...]]
            geometry.put("coordinates", Arrays.asList(polygon));
            contour.put("geometry", geometry);

            Map<String, Object> properties = new HashMap<>();
            properties.put("precipitationLevel", level);
            properties.put("createTime", new Date().toString());
            contour.put("properties", properties);

            precipitationData.computeIfAbsent(level, k -> new ArrayList<>()).add(contour);
        }
    }

    /**
     * 智能识别降水等级：根据数值范围自动分类
     */
    private String mapLevelToString(double level) {
        // 根据降水量范围智能识别等级
        if (level >= 0.1 && level < 10.0) {
            return "level0"; // 小雨：0.1-9.9mm
        } else if (level >= 10.0 && level < 25.0) {
            return "level10"; // 中雨：10-24.9mm
        } else if (level >= 25.0 && level < 50.0) {
            return "level25"; // 大雨：25-49.9mm
        } else if (level >= 50.0 && level < 100.0) {
            return "level50"; // 暴雨：50-99.9mm
        } else if (level >= 100.0 && level < 250.0) {
            return "level100"; // 大暴雨：100-249.9mm
        } else if (level >= 250.0) {
            return "level250"; // 特大暴雨：≥250mm
        } else {
            // 小于0.1的值或无效值，默认返回原始level标识
            return "level" + level;
        }
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        if (filename == null || filename.lastIndexOf('.') == -1) {
            return "";
        }
        return filename.substring(filename.lastIndexOf('.') + 1).toLowerCase();
    }

    /**
     * 判断是否为MICAPS文件
     */
    private boolean isMicapsFile(String extension) {
        String[] micapsExtensions = {"000", "024", "dat", "txt", "nc", "grib", "grib2", "cma"};
        for (String ext : micapsExtensions) {
            if (ext.equals(extension)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断是否为压缩文件
     */
    private boolean isArchiveFile(String extension) {
        String[] archiveExtensions = {"zip", "rar", "7z", "tar", "gz", "bz2"};
        for (String ext : archiveExtensions) {
            if (ext.equals(extension)) {
                return true;
            }
        }
        return false;
    }



    /**
     * 生成唯一文件名
     */
    private String generateUniqueFileName(String originalFilename) {
        String extension = getFileExtension(originalFilename);
        String uuid = UUID.randomUUID().toString().replace("-", "");
        return uuid + (extension.isEmpty() ? "" : "." + extension);
    }

    /**
     * 优化多边形，处理自相交问题
     * 如果多边形自相交，分离出多个独立的多边形，并保留面积最大的区域
     */
    private List<List<List<Double>>> optimizePolygon(List<List<Double>> coordinates) {
        List<List<List<Double>>> result = new ArrayList<>();

        try {
            // 检查是否存在自相交
            if (!hasSelfIntersection(coordinates)) {
                // 没有自相交，直接返回原多边形
                result.add(coordinates);
                return result;
            }

            log.info("检测到自相交多边形，开始分离处理");

            // 分离自相交的多边形
            List<List<List<Double>>> separatedPolygons = separateSelfIntersectingPolygon(coordinates);

            if (separatedPolygons.isEmpty()) {
                // 分离失败，返回原多边形
                result.add(coordinates);
                return result;
            }

            // 计算每个多边形的面积，保留最大的
            List<List<Double>> largestPolygon = findLargestPolygon(separatedPolygons);
            if (largestPolygon != null) {
                result.add(largestPolygon);
                log.info("保留面积最大的多边形，删除了 {} 个较小区域", separatedPolygons.size() - 1);
            } else {
                // 如果计算失败，返回原多边形
                result.add(coordinates);
            }

        } catch (Exception e) {
            log.error("多边形优化失败，使用原始多边形", e);
            result.add(coordinates);
        }

        return result;
    }

    /**
     * 检查多边形是否存在自相交
     */
    private boolean hasSelfIntersection(List<List<Double>> coordinates) {
        if (coordinates.size() < 4) {
            return false; // 少于4个点无法形成自相交
        }

        // 检查每条边是否与其他边相交（除了相邻边）
        for (int i = 0; i < coordinates.size() - 1; i++) {
            List<Double> p1 = coordinates.get(i);
            List<Double> p2 = coordinates.get((i + 1) % (coordinates.size() - 1));

            for (int j = i + 2; j < coordinates.size() - 1; j++) {
                // 跳过相邻边和最后一条边与第一条边的检查（它们本来就应该相交）
                if (j == coordinates.size() - 2 && i == 0) {
                    continue;
                }

                List<Double> p3 = coordinates.get(j);
                List<Double> p4 = coordinates.get((j + 1) % (coordinates.size() - 1));

                if (doLinesIntersect(p1, p2, p3, p4)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 检查两条线段是否相交
     */
    private boolean doLinesIntersect(List<Double> p1, List<Double> p2, List<Double> p3, List<Double> p4) {
        double x1 = p1.get(0), y1 = p1.get(1);
        double x2 = p2.get(0), y2 = p2.get(1);
        double x3 = p3.get(0), y3 = p3.get(1);
        double x4 = p4.get(0), y4 = p4.get(1);

        // 计算方向
        double d1 = direction(x3, y3, x4, y4, x1, y1);
        double d2 = direction(x3, y3, x4, y4, x2, y2);
        double d3 = direction(x1, y1, x2, y2, x3, y3);
        double d4 = direction(x1, y1, x2, y2, x4, y4);

        // 检查是否相交
        if (((d1 > 0 && d2 < 0) || (d1 < 0 && d2 > 0)) &&
            ((d3 > 0 && d4 < 0) || (d3 < 0 && d4 > 0))) {
            return true;
        }

        // 检查共线情况
        if (d1 == 0 && onSegment(x3, y3, x1, y1, x4, y4)) return true;
        if (d2 == 0 && onSegment(x3, y3, x2, y2, x4, y4)) return true;
        if (d3 == 0 && onSegment(x1, y1, x3, y3, x2, y2)) return true;
        if (d4 == 0 && onSegment(x1, y1, x4, y4, x2, y2)) return true;

        return false;
    }

    /**
     * 计算方向
     */
    private double direction(double xi, double yi, double xj, double yj, double xk, double yk) {
        return (xk - xi) * (yj - yi) - (xj - xi) * (yk - yi);
    }

    /**
     * 检查点是否在线段上
     */
    private boolean onSegment(double xi, double yi, double xk, double yk, double xj, double yj) {
        return Math.min(xi, xj) <= xk && xk <= Math.max(xi, xj) &&
               Math.min(yi, yj) <= yk && yk <= Math.max(yi, yj);
    }

    /**
     * 分离自相交的多边形
     * 这是一个简化的实现，实际的多边形分离算法比较复杂
     */
    private List<List<List<Double>>> separateSelfIntersectingPolygon(List<List<Double>> coordinates) {
        List<List<List<Double>>> result = new ArrayList<>();

        try {
            // 找到所有交点
            List<IntersectionPoint> intersections = findAllIntersections(coordinates);

            if (intersections.isEmpty()) {
                result.add(coordinates);
                return result;
            }

            // 简化处理：如果存在自相交，尝试创建多个简单的多边形
            // 这里使用一个简化的算法，将多边形分割成多个部分
            List<List<List<Double>>> segments = splitPolygonAtIntersections(coordinates, intersections);

            // 过滤掉无效的多边形（少于3个点的）
            for (List<List<Double>> segment : segments) {
                if (segment.size() >= 4) { // 至少3个不同的点加上闭合点
                    result.add(segment);
                }
            }

        } catch (Exception e) {
            log.error("分离自相交多边形失败", e);
            result.add(coordinates);
        }

        return result;
    }

    /**
     * 找到所有交点
     */
    private List<IntersectionPoint> findAllIntersections(List<List<Double>> coordinates) {
        List<IntersectionPoint> intersections = new ArrayList<>();

        for (int i = 0; i < coordinates.size() - 1; i++) {
            List<Double> p1 = coordinates.get(i);
            List<Double> p2 = coordinates.get((i + 1) % (coordinates.size() - 1));

            for (int j = i + 2; j < coordinates.size() - 1; j++) {
                if (j == coordinates.size() - 2 && i == 0) {
                    continue; // 跳过首尾边
                }

                List<Double> p3 = coordinates.get(j);
                List<Double> p4 = coordinates.get((j + 1) % (coordinates.size() - 1));

                List<Double> intersection = getLineIntersection(p1, p2, p3, p4);
                if (intersection != null) {
                    intersections.add(new IntersectionPoint(intersection, i, j));
                }
            }
        }

        return intersections;
    }

    /**
     * 计算两条线段的交点
     */
    private List<Double> getLineIntersection(List<Double> p1, List<Double> p2, List<Double> p3, List<Double> p4) {
        double x1 = p1.get(0), y1 = p1.get(1);
        double x2 = p2.get(0), y2 = p2.get(1);
        double x3 = p3.get(0), y3 = p3.get(1);
        double x4 = p4.get(0), y4 = p4.get(1);

        double denom = (x1 - x2) * (y3 - y4) - (y1 - y2) * (x3 - x4);
        if (Math.abs(denom) < 1e-10) {
            return null; // 平行线
        }

        double t = ((x1 - x3) * (y3 - y4) - (y1 - y3) * (x3 - x4)) / denom;
        double u = -((x1 - x2) * (y1 - y3) - (y1 - y2) * (x1 - x3)) / denom;

        if (t >= 0 && t <= 1 && u >= 0 && u <= 1) {
            double x = x1 + t * (x2 - x1);
            double y = y1 + t * (y2 - y1);
            return Arrays.asList(x, y);
        }

        return null;
    }

    /**
     * 在交点处分割多边形
     */
    private List<List<List<Double>>> splitPolygonAtIntersections(List<List<Double>> coordinates,
                                                                List<IntersectionPoint> intersections) {
        List<List<List<Double>>> result = new ArrayList<>();

        if (intersections.isEmpty()) {
            result.add(coordinates);
            return result;
        }

        // 简化处理：如果有交点，尝试创建两个子多边形
        // 这是一个基础实现，复杂的自相交多边形可能需要更高级的算法
        try {
            IntersectionPoint firstIntersection = intersections.get(0);
            List<Double> intersectionPoint = firstIntersection.point;

            // 创建第一个子多边形
            List<List<Double>> polygon1 = new ArrayList<>();
            for (int i = 0; i <= firstIntersection.segmentIndex1; i++) {
                polygon1.add(coordinates.get(i));
            }
            polygon1.add(intersectionPoint);
            for (int i = firstIntersection.segmentIndex2 + 1; i < coordinates.size(); i++) {
                polygon1.add(coordinates.get(i));
            }

            // 创建第二个子多边形
            List<List<Double>> polygon2 = new ArrayList<>();
            polygon2.add(intersectionPoint);
            for (int i = firstIntersection.segmentIndex1 + 1; i <= firstIntersection.segmentIndex2; i++) {
                polygon2.add(coordinates.get(i));
            }
            polygon2.add(intersectionPoint);

            result.add(polygon1);
            result.add(polygon2);

        } catch (Exception e) {
            log.error("分割多边形失败", e);
            result.add(coordinates);
        }

        return result;
    }

    /**
     * 找到面积最大的多边形
     */
    private List<List<Double>> findLargestPolygon(List<List<List<Double>>> polygons) {
        if (polygons.isEmpty()) {
            return null;
        }

        List<List<Double>> largestPolygon = null;
        double maxArea = 0;

        for (List<List<Double>> polygon : polygons) {
            double area = calculatePolygonArea(polygon);
            if (area > maxArea) {
                maxArea = area;
                largestPolygon = polygon;
            }
        }

        return largestPolygon;
    }

    /**
     * 计算多边形面积（使用鞋带公式）
     */
    private double calculatePolygonArea(List<List<Double>> coordinates) {
        if (coordinates.size() < 3) {
            return 0;
        }

        double area = 0;
        int n = coordinates.size();

        for (int i = 0; i < n - 1; i++) {
            List<Double> p1 = coordinates.get(i);
            List<Double> p2 = coordinates.get((i + 1) % n);
            area += p1.get(0) * p2.get(1) - p2.get(0) * p1.get(1);
        }

        return Math.abs(area) / 2.0;
    }

    /**
     * 交点信息类
     */
    private static class IntersectionPoint {
        List<Double> point;
        int segmentIndex1;
        int segmentIndex2;

        public IntersectionPoint(List<Double> point, int segmentIndex1, int segmentIndex2) {
            this.point = point;
            this.segmentIndex1 = segmentIndex1;
            this.segmentIndex2 = segmentIndex2;
        }
    }
}
